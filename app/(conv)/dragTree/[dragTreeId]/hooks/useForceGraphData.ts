import { useMemo } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store/store'
import { useFilterStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useFilterStore'
import { pruneTreeByFilter } from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeFilter'
import type { TreeNode } from '@/app/types'

type FGNode = {
  id: string
  name: string
  type: 'category' | 'question'
  level: number
  deg?: number
  isRoot?: boolean
}
type FGLink = { source: string; target: string }

export const useForceGraphData = (): { nodes: FGNode[]; links: FGLink[] } => {
  const tree = useDragTreeStore(state => state.frontendTreeStructure)
  const getNodeContent = useDragTreeStore(state => state.getNodeContent)
  const mode = useFilterStore(state => state.mode)

  // Apply the same filtering used for ReactFlow before visualization
  const filteredTree = useMemo<TreeNode | null>(() => {
    return pruneTreeByFilter(tree, mode, getNodeContent)
  }, [tree, mode, getNodeContent])

  const { nodes, links } = useMemo(() => {
    const n: FGNode[] = []
    const l: FGLink[] = []
    if (!filteredTree) return { nodes: n, links: l }

    const degree = new Map<string, number>()

    const visit = (node: TreeNode, parentId: string | null, level: number) => {
      n.push({
        id: node.id,
        name: node.label,
        type: node.type,
        level,
        isRoot: level === 1,
      })
      if (parentId) l.push({ source: parentId, target: node.id })
      node.children.forEach(child => visit(child, node.id, level + 1))
    }

    visit(filteredTree, null, 1)

    // compute degree counts
    for (const link of l) {
      degree.set(link.source, (degree.get(link.source) || 0) + 1)
      degree.set(link.target, (degree.get(link.target) || 0) + 1)
    }
    n.forEach(node => (node.deg = degree.get(node.id) || 0))
    return { nodes: n, links: l }
  }, [filteredTree])

  return { nodes, links }
}
