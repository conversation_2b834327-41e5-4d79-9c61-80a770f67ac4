'use client'

import React, { useEffect, useRef, useCallback } from 'react'
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  useReactFlow,
  useNodesInitialized,
  type OnMove,
  type Viewport,
  type Node as FlowNode,
} from 'reactflow'
import 'reactflow/dist/style.css'

import { useDiagramData } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useDiagramData'
import { useDiagramNavigation } from './hooks/useDiagramNavigation'
import { useFlowImageDownload } from './hooks/useFlowImageDownload'
import { useNodeSelection } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useNodeSelection'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useSelectionCleanup } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useSelectionCleanup'
import { useSelectionKeyboard } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useSelectionKeyboard'
import { nodeTypes } from './nodes/index'
import { LayoutModeToggle } from './LayoutModeToggle'
import { SelectionModeToggle } from './SelectionModeToggle'
import { SelectionOverlay } from './SelectionOverlay'
import { SelectionControls } from './SelectionControls'
import { NavigationWidget } from './NavigationWidget'
import { SelectionZoomControls } from './SelectionZoomControls'
import { cn } from '@/lib/utils'
import { useNavigationStore } from '@/app/stores/navigation_store'

type DiagramViewProps = {
  layoutMode: 'linear' | 'radial'
  // Accept broader union so the toggle can switch to other modes
  setLayoutMode: (mode: 'linear' | 'radial' | 'force' | 'force3d') => void
  dragTreeId: string
}

import { useReactFlowViewStore } from '@/app/stores/reactflow_view_store'

const ReactFlowCanvas: React.FC<DiagramViewProps> = ({
  layoutMode,
  setLayoutMode,
  dragTreeId,
}) => {
  const { diagramData, layoutPromise } = useDiagramData(layoutMode)

  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const { fitView, setViewport, getNodes } = useReactFlow()
  // Select only needed actions from the store to avoid rerender loops
  const getSavedViewport = useReactFlowViewStore(state => state.getViewport)
  const saveViewport = useReactFlowViewStore(state => state.saveViewport)
  const loadViewportFromLS = useReactFlowViewStore(
    state => state.loadFromLocalStorage
  )
  const nodesInitialized = useNodesInitialized()
  const { downloadImage } = useFlowImageDownload(layoutMode, setLayoutMode)
  // Selection hooks - used for side effects
  useNodeSelection()
  const isSelectionModeFlag = useSelectionStore(state => state.isSelectionMode)
  useSelectionCleanup()
  useSelectionKeyboard()
  const initialFocusRef = useRef<boolean>(false)
  const currentLayoutModeRef = useRef<'linear' | 'radial'>(layoutMode)
  const lastValidViewportRef = useRef<Viewport>({ x: 0, y: 0, zoom: 0.5 })
  const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [minimapError, setMinimapError] = React.useState<boolean>(false)
  const [radialViewReady, setRadialViewReady] = React.useState<boolean>(
    layoutMode !== 'radial'
  )

  const findRootNode = useCallback(
    (nodesToSearch: any[]) => {
      if (!nodesToSearch || nodesToSearch.length === 0) return null
      let rootNode = nodesToSearch.find((n: any) => n.data?.level === 1)
      if (rootNode) return rootNode
      rootNode = nodesToSearch.find((n: any) => !n.data?.parentId)
      if (rootNode) return rootNode
      const targetNodeIds = new Set(
        (edges || []).map((edge: any) => edge.target)
      )
      rootNode = nodesToSearch.find((n: any) => !targetNodeIds.has(n.id))
      if (rootNode) return rootNode
      return nodesToSearch[0] || null
    },
    [edges]
  )

  const areNodesProperlyPositioned = useCallback((nodesToCheck: any[]) => {
    if (nodesToCheck.length === 0) return false
    const validPositions = nodesToCheck.filter(
      (node: any) =>
        isFinite(node.position.x) &&
        isFinite(node.position.y) &&
        !isNaN(node.position.x) &&
        !isNaN(node.position.y)
    )
    if (validPositions.length === 0) return false
    const wellPositioned = validPositions.filter(
      (node: any) =>
        Math.abs(node.position.x) > 50 || Math.abs(node.position.y) > 50
    )
    const positionedRatio = wellPositioned.length / validPositions.length
    return positionedRatio > 0.7
  }, [])

  const focusOnRootNode = useCallback(
    (nodesToFocus: any[], attempt: number = 0) => {
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current)
        focusTimeoutRef.current = null
      }
      if (initialFocusRef.current) {
        return
      }
      const maxAttempts = 5
      const baseDelay = layoutMode === 'radial' ? 200 : 100
      const delay = Math.min(baseDelay * Math.pow(1.5, attempt), 2000)

      const rootNode = findRootNode(nodesToFocus)
      if (!rootNode) {
        return
      }

      if (
        layoutMode === 'radial' &&
        !areNodesProperlyPositioned(nodesToFocus)
      ) {
        if (attempt < maxAttempts - 1) {
          focusTimeoutRef.current = setTimeout(() => {
            focusOnRootNode(getNodes(), attempt + 1)
          }, delay)
          return
        }
      }

      try {
        if (layoutMode === 'radial') {
          fitView({ duration: 500, padding: 0.3, maxZoom: 0.5 })
        } else {
          fitView({
            nodes: [{ id: rootNode.id }],
            duration: 500,
            padding: 0.3,
            maxZoom: 0.5,
          })
        }
        initialFocusRef.current = true
        if (layoutMode === 'radial') {
          setRadialViewReady(true)
        }
      } catch (_err) {
        if (attempt < maxAttempts - 1) {
          focusTimeoutRef.current = setTimeout(() => {
            focusOnRootNode(getNodes(), attempt + 1)
          }, delay)
        }
      }
    },
    [layoutMode, findRootNode, areNodesProperlyPositioned, fitView, getNodes]
  )

  const isValidViewport = useCallback((viewport: Viewport): boolean => {
    const { x, y, zoom } = viewport
    return (
      isFinite(x) &&
      isFinite(y) &&
      isFinite(zoom) &&
      !isNaN(x) &&
      !isNaN(y) &&
      !isNaN(zoom) &&
      zoom > 0 &&
      zoom <= 10
    )
  }, [])

  const triggerViewportRecovery = useCallback(() => {
    if (recoveryTimeoutRef.current) {
      clearTimeout(recoveryTimeoutRef.current)
    }
    recoveryTimeoutRef.current = setTimeout(() => {
      try {
        setMinimapError(false)
        if (isValidViewport(lastValidViewportRef.current)) {
          setViewport(lastValidViewportRef.current, { duration: 300 })
        } else {
          fitView({ duration: 500, padding: 0.3, maxZoom: 2.0, minZoom: 0.1 })
        }
        setTimeout(() => setMinimapError(false), 500)
      } catch (_error) {
        setMinimapError(true)
        setTimeout(() => {
          fitView({ duration: 0 })
          setMinimapError(false)
        }, 100)
      }
    }, 150)
  }, [isValidViewport, setViewport, fitView])

  const handleViewportMove: OnMove = useCallback(
    (_event, viewport) => {
      if (isValidViewport(viewport)) {
        lastValidViewportRef.current = { ...viewport }
      } else {
        triggerViewportRecovery()
      }
    },
    [isValidViewport, triggerViewportRecovery]
  )

  const handleViewportMoveEnd: OnMove = useCallback(
    (_event, viewport) => {
      if (!isValidViewport(viewport)) return
      lastValidViewportRef.current = { ...viewport }
      if (dragTreeId) {
        saveViewport(dragTreeId, viewport as any)
      }
    },
    [dragTreeId, saveViewport, isValidViewport]
  )

  useEffect(() => {
    // On mount, try to restore saved viewport for this tree
    if (dragTreeId) {
      loadViewportFromLS(dragTreeId)
      const saved = getSavedViewport(dragTreeId)
      if (saved && isValidViewport(saved as any)) {
        setViewport(saved as any, { duration: 0 })
        lastValidViewportRef.current = saved as any
      }
    }

    layoutPromise.then(layout => {
      setNodes(layout.nodes || diagramData.nodes)
      setEdges(layout.edges || diagramData.edges)
      initialFocusRef.current = false
      const nodesToFocus = layout.nodes || diagramData.nodes
      if (nodesToFocus && nodesToFocus.length > 0) {
        const delay = layoutMode === 'radial' ? 100 : 50
        setTimeout(() => {
          // If we restored a saved viewport, do not auto-fit; otherwise focus root
          const saved = dragTreeId ? getSavedViewport(dragTreeId) : null
          if (!saved) {
            focusOnRootNode(nodesToFocus)
          }
        }, delay)
      }
    })
  }, [
    layoutPromise,
    diagramData,
    setNodes,
    setEdges,
    layoutMode,
    focusOnRootNode,
    dragTreeId,
    setViewport,
    isValidViewport,
    loadViewportFromLS,
    getSavedViewport,
  ])

  useDiagramNavigation(nodes)

  const handleNodeClick = (event: React.MouseEvent, _node: FlowNode) => {
    event.preventDefault()
    event.stopPropagation()
    try {
      const navigateToTreeNodeFromReactFlow =
        useNavigationStore.getState().navigateToTreeNodeFromReactFlow
      if (typeof navigateToTreeNodeFromReactFlow === 'function' && _node?.id) {
        navigateToTreeNodeFromReactFlow(_node.id)
      }
    } catch {}
  }

  useEffect(() => {
    if (currentLayoutModeRef.current !== layoutMode) {
      currentLayoutModeRef.current = layoutMode
      initialFocusRef.current = false
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current)
        focusTimeoutRef.current = null
      }
      if (layoutMode === 'radial') {
        setRadialViewReady(false)
      } else {
        setRadialViewReady(true)
      }
    }
  }, [layoutMode])

  useEffect(() => {
    if (nodes.length > 0 && nodesInitialized && !initialFocusRef.current) {
      setTimeout(() => {
        if (!initialFocusRef.current) {
          focusOnRootNode(nodes)
        }
      }, 200)
    }
  }, [nodes, nodesInitialized, focusOnRootNode])

  useEffect(() => {
    const handleDownloadEvent = async (event: Event) => {
      const { layoutMode: requestedLayoutMode } =
        (event as CustomEvent).detail || {}
      window.dispatchEvent(
        new CustomEvent('reactflow-download-start', {
          detail: { layoutMode: requestedLayoutMode },
        })
      )
      try {
        await downloadImage(requestedLayoutMode)
      } finally {
        window.dispatchEvent(
          new CustomEvent('reactflow-download-end', {
            detail: { layoutMode: requestedLayoutMode },
          })
        )
      }
    }
    window.addEventListener('reactflow-download', handleDownloadEvent)
    return () => {
      window.removeEventListener('reactflow-download', handleDownloadEvent)
    }
  }, [downloadImage])

  const hasSentReadyEvent = React.useRef(false)
  useEffect(() => {
    if (!hasSentReadyEvent.current && nodes.length > 0 && nodesInitialized) {
      window.dispatchEvent(new CustomEvent('reactflow-diagram-ready'))
      hasSentReadyEvent.current = true
    }
  }, [nodes, nodesInitialized])

  useEffect(() => {
    return () => {
      if (recoveryTimeoutRef.current) clearTimeout(recoveryTimeoutRef.current)
      if (focusTimeoutRef.current) clearTimeout(focusTimeoutRef.current)
      setMinimapError(false)
      setRadialViewReady(layoutMode !== 'radial')
    }
  }, [])

  const MiniMapWithErrorBoundary: React.FC = () => {
    const [hasError, setHasError] = React.useState<boolean>(false)
    React.useEffect(() => {
      if (!minimapError) setHasError(false)
    }, [minimapError])
    React.useEffect(() => {
      const handleError = (event: ErrorEvent) => {
        if (!event?.message) return
        if (
          event.message.includes('viewBox') ||
          event.message.includes('NaN')
        ) {
          setHasError(true)
          setMinimapError(true)
          triggerViewportRecovery()
        }
      }
      window.addEventListener('error', handleError)
      return () => window.removeEventListener('error', handleError)
    }, [triggerViewportRecovery])
    if (hasError || minimapError) {
      return (
        <div
          className="opacity-75 hover:opacity-95 transition-all duration-300 shadow-lg hover:shadow-xl"
          style={{
            position: 'absolute',
            bottom: '20px',
            right: '20px',
            width: '150px',
            height: '100px',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid rgba(0, 0, 0, 0.15)',
            borderRadius: '12px',
            backdropFilter: 'blur(8px)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            color: '#64748b',
            cursor: 'pointer',
          }}
          onClick={() => {
            setHasError(false)
            setMinimapError(false)
            triggerViewportRecovery()
          }}
        >
          <div className="text-center">
            <div>MiniMap</div>
            <div>Recovering...</div>
            <div className="text-xs mt-1">Click to retry</div>
          </div>
        </div>
      )
    }
    try {
      return (
        <MiniMap
          nodeColor={n => {
            if (n.type === 'customCategoryNode') return '#9333ea'
            if (n.type === 'customQuestionNode') return '#22c55e'
            return '#cbd5e1'
          }}
          nodeStrokeWidth={3}
          zoomable
          pannable
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            border: '1px solid rgba(0, 0, 0, 0.15)',
            borderRadius: '12px',
            backdropFilter: 'blur(8px)',
          }}
          className="opacity-75 hover:opacity-95 transition-all duration-300 shadow-lg hover:shadow-xl"
        />
      )
    } catch (_error) {
      setHasError(true)
      return null
    }
  }

  return (
    <div
      className={cn(
        'h-full w-full relative transition-colors duration-200',
        isSelectionModeFlag && 'ring-4 ring-emerald-400/60'
      )}
      style={{
        visibility:
          layoutMode === 'radial' && !radialViewReady ? 'hidden' : 'visible',
      }}
    >
      <LayoutModeToggle layoutMode={layoutMode} setLayoutMode={setLayoutMode} />
      <SelectionModeToggle className="absolute top-4 right-4 z-30" />
      {/* Main ReactFlow surface */}
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={handleNodeClick}
        onMove={handleViewportMove}
        onMoveEnd={handleViewportMoveEnd}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={{
          type: 'smoothstep',
          style: { stroke: '#64748b', strokeWidth: 2 },
        }}
        defaultViewport={{ x: 0, y: 0, zoom: 0.5 }}
        minZoom={0.1}
        maxZoom={3.0}
        translateExtent={[
          [-20000, -20000],
          [20000, 20000],
        ]}
        panOnDrag={true}
        panOnScroll={true}
        zoomOnScroll={true}
        zoomOnPinch={false}
        zoomOnDoubleClick={false}
        preventScrolling={false}
        onInit={() => {
          window.dispatchEvent(new CustomEvent('reactflow-ready'))
        }}
        className={cn(
          'transition-all duration-500',
          layoutMode === 'radial'
            ? 'bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50'
            : 'bg-gradient-to-br from-slate-50 to-stone-100'
        )}
        proOptions={{ hideAttribution: true }}
      >
        <Controls className="react-flow-controls" />
        <MiniMapWithErrorBoundary />
        <Background gap={24} />
      </ReactFlow>

      {/* Overlays and widgets above ReactFlow */}
      <SelectionOverlay />
      <SelectionControls />
      <NavigationWidget />
      <SelectionZoomControls />
    </div>
  )
}

export default React.memo(ReactFlowCanvas)
