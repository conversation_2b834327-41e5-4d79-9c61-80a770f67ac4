import { streamText, stepCountIs, createIdGenerator } from 'ai'
import { auth } from '@/auth'
import { NextRequest } from 'next/server'
import { enforceRateLimit } from '@/app/libs/llmRateLimit'
import {
  ExecutionStepCollector,
  persistConversationTurn,
  finalizeConversationTurn,
} from '@/app/server-actions/ai-chat'
import {
  RETRY_CONFIG,
  computeBackoffDelay,
} from '@/app/api/aipane/chat/chat-config'
import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'
import {
  getModelConfigFromSession,
  getModelFromConfig,
  getProviderNameForUsage,
} from '@/app/libs/model-config'

// Compact title from user's first message (heuristic for low cost, no LLM)
function generateChatTitleFromText(text: string): string {
  const t = (text || '').trim().replace(/\s+/g, ' ')
  if (!t) return 'Untitled'
  const firstLine = t.split('\n')[0]
  const normalized = firstLine.replace(/^["'""'']+|["'""'']+$/g, '')
  const max = 50
  if (normalized.length <= max) return normalized
  const idx = normalized.lastIndexOf(' ', max)
  return (
    (idx > 20 ? normalized.slice(0, idx) : normalized.slice(0, max)) + '...'
  )
}

// Types for the improved API
type ChatV2Request = {
  message: string // Only the new user message
  conversationId: string
  context?: string // Only included on first message or when context changes
  model?: string
  contextIds?: string[]
  dragTreeId?: string
}

/**
 * Chat v2 API Route - Improved Architecture
 *
 * Key improvements:
 * - API handles message history retrieval internally
 * - Frontend sends only new user message
 * - Context is handled as system message server-side
 * - Reduced payload size for long conversations
 * - Better separation of concerns
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const session = await auth()
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 })
    }
    const userId = session.user.id

    // Distributed rate-limit per user & route (KV-backed if configured)
    {
      const rl = await enforceRateLimit(session, 'aipane_chat')
      if (rl) return rl
    }

    // Parse request body
    const body: ChatV2Request = await request.json()
    const {
      message,
      conversationId,
      context,
      contextIds = [],
      dragTreeId: _dragTreeId,
    } = body

    // Simple 3-step flow:
    // 1. Get tier (done inside getModelConfig)
    // 2. Get config
    const modelConfig = await getModelConfigFromSession(session, 'aipane_chat')
    const model = modelConfig.model

    // 3. Get model instance
    const modelInstance = getModelFromConfig(modelConfig)

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log('🚀 [Chat v2] Processing request:', {
        messageLength: message?.length,
        model,
        conversationId,
        hasContext: !!context,
        contextIds: contextIds?.length,
      })
    }

    // Validate required fields
    if (!message || typeof message !== 'string') {
      return new Response('Message is required', { status: 400 })
    }

    if (!conversationId || !conversationId.startsWith('thread_')) {
      return new Response('Valid conversation ID is required', { status: 400 })
    }

    // Ensure conversation exists (idempotent upsert) before any reads/writes
    // This prevents 404s on first load and FK violations when persisting messages
    try {
      await prisma.aiConversation.upsert({
        where: { id: conversationId },
        create: {
          id: conversationId,
          userId: session.user.id,
          title: 'Untitled chat',
          contextEntityType: _dragTreeId ? 'drag_tree' : undefined,
          contextEntityId: _dragTreeId || undefined,
          metadata: { contextIds: contextIds || [] },
        },
        update: {},
      })
    } catch (err) {
      console.error('❌ [Chat v2] Failed to upsert conversation:', err)
      return new Response('Failed to initialize conversation', { status: 500 })
    }

    // Retrieve conversation history from database
    const existingMessages = await prisma.aiMessage.findMany({
      where: { conversationId },
      select: {
        role: true,
        content: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'asc' },
    })

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log(
        `📚 [Chat v2] Retrieved ${existingMessages.length} existing messages`
      )
    }

    // Build context text from either provided direct text or stored contextIds
    let contextText: string | undefined
    try {
      if (typeof context === 'string' && context.trim().length > 0) {
        contextText = context.trim()
      } else {
        // Determine final contextIds to use (prefer request body; fallback to conversation metadata)
        const conv = await prisma.aiConversation.findUnique({
          where: { id: conversationId },
          select: {
            metadata: true,
            contextEntityType: true,
            contextEntityId: true,
          },
        })
        let finalContextIds: string[] = Array.isArray(contextIds)
          ? contextIds
          : []
        if (finalContextIds.length === 0 && conv?.metadata) {
          const meta = conv.metadata as any
          if (Array.isArray(meta?.contextIds)) {
            finalContextIds = meta.contextIds as string[]
          }
        }

        if (finalContextIds.length > 0) {
          // Fetch node labels and recent content to construct human-readable context
          try {
            const nodes = await prisma.dragTreeNode.findMany({
              where: { id: { in: finalContextIds } },
              select: { id: true, label: true },
            })

            // For each node, attempt to fetch the most recent ACTIVE content
            const contentByNodeId: Record<string, string> = {}
            await Promise.all(
              finalContextIds.map(async nodeId => {
                try {
                  const content = await prisma.dragTreeNodeContent.findFirst({
                    where: {
                      drag_tree_node_id: nodeId,
                      status: 'ACTIVE',
                    },
                    select: { content_text: true, updated_at: true },
                    orderBy: { updated_at: 'desc' },
                  })
                  if (content?.content_text) {
                    // Truncate per-item to avoid very large context
                    const txt = content.content_text.trim()
                    const snippet =
                      txt.length > 800 ? txt.slice(0, 800) + '…' : txt
                    contentByNodeId[nodeId] = snippet
                  }
                } catch {}
              })
            )

            if (nodes.length > 0) {
              const lines: string[] = []
              lines.push("Selected context items from user's workspace:")
              nodes.forEach((n, idx) => {
                const snippet = contentByNodeId[n.id]
                if (snippet) {
                  lines.push(`${idx + 1}. ${n.label}`)
                  lines.push('')
                  lines.push(snippet)
                  lines.push('')
                } else {
                  lines.push(`${idx + 1}. ${n.label}`)
                }
              })
              contextText = lines.join('\n')
            }
          } catch (e) {
            console.warn('[Chat v2] Failed to build detailed context:', e)
          }
        }
      }
    } catch (e) {
      console.warn('[Chat v2] Context construction warning:', e)
    }

    // Build message array for AI model
    const modelMessages: Array<{
      role: 'system' | 'user' | 'assistant'
      content: string
    }> = []

    // Add system message (include constructed context text when available)
    if (contextText && contextText.trim()) {
      modelMessages.push({
        role: 'system',
        content: `You are a helpful AI assistant with access to web search tools. Use the following context to help answer questions:

${contextText}

When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`,
      })
    } else {
      // Default system message without context
      modelMessages.push({
        role: 'system',
        content: `You are a helpful AI assistant with access to web search tools.
When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`,
      })
    }

    // Add existing conversation history (excluding system messages)
    existingMessages
      .filter(msg => msg.role !== 'SYSTEM')
      .forEach(msg => {
        modelMessages.push({
          role: msg.role.toLowerCase() as 'user' | 'assistant',
          content: msg.content,
        })
      })

    // Add the new user message
    modelMessages.push({
      role: 'user',
      content: message,
    })

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log(
        `💬 [Chat v2] Built ${modelMessages.length} messages for AI model`
      )
    }

    // Set up execution step collector for persistence
    const stepCollector = new ExecutionStepCollector()

    // Build search tools
    const tools = buildSearchTools([], () => {})

    // Stream the response using AI SDK v5

    const result = streamText({
      model: modelInstance,
      messages: modelMessages,
      tools,
      toolChoice: 'auto',
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens,
      // Enable multi-step tool calling
      stopWhen: stepCountIs(10),
      // Track tool calls for persistence
      onChunk: async chunk => {
        const kind = (chunk as any)?.chunk?.type as string | undefined
        if (kind === 'tool-call') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
        if (kind === 'tool-result') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
        if (kind === 'reasoning-delta') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
      },
    })

    // Helper to extract plain text from a UIMessage
    const extractTextFromUIMessage = (m: any): string => {
      if (!m) return ''
      if (typeof m.content === 'string' && m.content.trim().length > 0) {
        return m.content
      }
      if (Array.isArray(m.parts)) {
        return m.parts
          .filter(
            (p: any) => p && (p.type === 'text' || p.type === 'text-delta')
          )
          .map((p: any) => (p.type === 'text-delta' ? p.textDelta : p.text))
          .join('')
      }
      return ''
    }

    // Ensure stream completes and onFinish runs even if client disconnects
    result.consumeStream()

    // Persist using the UI messages provided by the stream response
    return result.toUIMessageStreamResponse({
      // Generate consistent server-side UI message IDs
      generateMessageId: createIdGenerator({ prefix: 'msg', size: 16 }),
      onFinish: async ({ messages: uiMessages, responseMessage }) => {
        try {
          const collectedSteps = stepCollector.getSteps()
          const hasSystemAlready = existingMessages.some(
            m => m.role === 'SYSTEM'
          )
          const nonSystemCount = existingMessages.filter(
            m => m.role !== 'SYSTEM'
          ).length
          const isFirstTurn = nonSystemCount === 0

          // Get last user and assistant UI messages
          const reversed = [...uiMessages].reverse()
          let lastUser = reversed.find(m => m.role === 'user') as any
          const lastAssistant = reversed.find(
            m => m.role === 'assistant'
          ) as any

          const userText = extractTextFromUIMessage(lastUser) || message
          let assistantText =
            extractTextFromUIMessage(lastAssistant) ||
            extractTextFromUIMessage(responseMessage as any) ||
            ''

          // Ensure assistant UI message has an id for FE key stability
          if (lastAssistant && !lastAssistant.id) {
            lastAssistant.id = `msg_assistant_${Date.now()}`
          }
          // If responseMessage exists but has empty id, assign one as well
          if (responseMessage && !(responseMessage as any).id) {
            ;(responseMessage as any).id = `msg_assistant_${Date.now()}_resp`
          }

          // Synthesize user UIMessage if provider did not include it in final.messages
          if (!lastUser && userText) {
            lastUser = {
              id: `msg_user_${Date.now()}`,
              role: 'user',
              parts: [{ type: 'text', text: userText }],
            }
          }

          const MAX_RETRIES = RETRY_CONFIG.MAX_RETRIES
          let attempt = 0
          let ok = false
          let lastError: any = null

          while (attempt < MAX_RETRIES && !ok) {
            attempt++
            try {
              if (
                isFirstTurn &&
                contextText &&
                contextText.trim().length > 0 &&
                !hasSystemAlready
              ) {
                const res = await finalizeConversationTurn(
                  conversationId,
                  {
                    role: 'USER',
                    content: userText,
                    uiMessage: lastUser,
                  },
                  {
                    role: 'ASSISTANT',
                    content: assistantText || '',
                    steps: collectedSteps,
                    uiMessage: lastAssistant,
                    metadata: {
                      model,
                      contextIds,
                      executionStepCount: collectedSteps.length,
                    },
                  },
                  { contextIds },
                  contextText
                )
                ok = res.success
                if (!ok) lastError = res.error
              } else {
                const res = await persistConversationTurn({
                  conversationId,
                  userMessage: {
                    role: 'USER',
                    content: userText,
                    uiMessage: lastUser,
                  },
                  assistantMessage: {
                    role: 'ASSISTANT',
                    content: assistantText || '',
                    steps: collectedSteps,
                    uiMessage: lastAssistant,
                    metadata: {
                      model,
                      contextIds,
                      executionStepCount: collectedSteps.length,
                    },
                  },
                })
                ok = res.success
                if (!ok) lastError = res.error
              }

              if (!ok) {
                const ms = computeBackoffDelay(
                  attempt,
                  RETRY_CONFIG.BACKOFF_MS,
                  RETRY_CONFIG.STRATEGY
                )
                await new Promise(res => setTimeout(res, ms))
              }
            } catch (err) {
              lastError = err
              const ms = computeBackoffDelay(
                attempt,
                RETRY_CONFIG.BACKOFF_MS,
                RETRY_CONFIG.STRATEGY
              )
              await new Promise(res => setTimeout(res, ms))
            }
          }

          if (!ok) {
            console.error(
              '❌ [Chat v2] Persist failed after retries:',
              lastError
            )
          } else if (ENABLE_CHAT_DEBUG_LOGGING) {
            console.log('💾 [Chat v2] Messages persisted successfully', {
              stepCount: collectedSteps.length,
              firstTurn: isFirstTurn,
            })
          }

          if (ok && isFirstTurn) {
            try {
              // Prefer an LLM-generated concise title using context + user question
              let finalTitle: string | null = null
              try {
                const { generateTitleWithWeakModel } = await import(
                  '@/app/libs/title-generation'
                )
                const seed = [
                  contextText ? `Context:\n${contextText}` : null,
                  `Question: ${userText}`,
                ]
                  .filter(Boolean)
                  .join('\n\n')
                finalTitle = (
                  await generateTitleWithWeakModel(seed, userId, conversationId)
                ).trim()
              } catch {
                // Fallback to heuristic if weak model unavailable
                finalTitle = generateChatTitleFromText(userText)
              }

              await prisma.aiConversation.update({
                where: { id: conversationId },
                data: { title: finalTitle || 'Untitled chat' },
              })
              // Also write the title as a step for observability (optional)
              try {
                await prisma.aiMessage.create({
                  data: {
                    conversationId,
                    role: 'SYSTEM',
                    content: `TITLE:\n${finalTitle}`,
                  },
                })
              } catch {}
              if (ENABLE_CHAT_DEBUG_LOGGING) {
                console.log('📝 [Chat v2] Set conversation title:', {
                  conversationId,
                  title: finalTitle,
                })
              }
            } catch (err) {
              console.warn('⚠️ [Chat v2] Failed to set title:', err)
            }
          }

          // Log AI usage for analytics and billing
          try {
            await createAIUsage({
              userId: session.user.id,
              entityType: 'aipane',
              entityId: conversationId,
              aiProvider: getProviderNameForUsage(modelConfig),
              modelName: model,
              usageType: AIUsageType.CHAT,
              inputPrompt: userText,
              messages: [
                ...modelMessages,
                { role: 'assistant', content: assistantText || '' },
              ],
              metadata: {
                endpoint: '/api/aipane/chat-v2',
                inputTokens: 0,
                outputTokens: 0,
                totalTokens: 0,
                finishReason: undefined,
                hasContext: !!contextText,
                messageCount: uiMessages.length,
                conversationId,
                executionSteps: stepCollector.getStepCount(),
              },
              config: {},
            })
          } catch (error) {
            console.error('Failed to log AI usage (v2):', error)
          }
        } catch (error) {
          console.error('❌ [Chat v2] Persistence error:', error)
        }
      },
    })
  } catch (error) {
    console.error('❌ [Chat v2] API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
